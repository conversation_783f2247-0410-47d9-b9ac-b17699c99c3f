"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.toolRegistry = exports.ToolRegistry = exports.allTools = exports.manageDependenciesTool = exports.monitorSystemTool = exports.manageEnvironmentTool = exports.validateJsonTool = exports.systemInfoTool = exports.networkRequestTool = exports.executeParallelToolsTool = exports.analyzeCodeTool = exports.watchDirectoryTool = exports.getSessionInfoTool = exports.getProjectContextTool = exports.listProcessesTool = exports.killProcessTool = exports.executeScriptTool = exports.executeCommandTool = exports.setPermissionsTool = exports.getFileInfoTool = exports.grepFilesTool = exports.searchFilesTool = exports.listDirectoryTool = exports.createDirectoryTool = exports.moveFileTool = exports.copyFileTool = exports.deleteFileTool = exports.writeFileTool = exports.readFileTool = void 0;
const zod_1 = require("zod");
const file_ops_1 = require("../operations/file-ops");
const shell_ops_1 = require("../operations/shell-ops");
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
// File operation tools
exports.readFileTool = {
    name: 'read_file',
    description: 'Read the contents of a file',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the file to read'),
    }),
    execute: async (params, context) => {
        const { filePath } = params;
        return await file_ops_1.fileOperations.readFile(filePath, context);
    },
};
exports.writeFileTool = {
    name: 'write_file',
    description: 'Write content to a file',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the file to write'),
        content: zod_1.z.string().describe('Content to write to the file'),
        overwrite: zod_1.z.boolean().optional().describe('Whether to overwrite existing file'),
        createDirs: zod_1.z.boolean().optional().describe('Whether to create parent directories'),
    }),
    execute: async (params, context) => {
        const { filePath, content, overwrite, createDirs } = params;
        return await file_ops_1.fileOperations.writeFile(filePath, content, context, {
            overwrite: overwrite ?? false,
            createDirs: createDirs ?? true
        });
    },
};
exports.deleteFileTool = {
    name: 'delete_file',
    description: 'Delete a file or directory',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the file or directory to delete'),
    }),
    execute: async (params, context) => {
        const { filePath } = params;
        return await file_ops_1.fileOperations.deleteFile(filePath, context);
    },
};
exports.copyFileTool = {
    name: 'copy_file',
    description: 'Copy a file from source to destination',
    parameters: zod_1.z.object({
        sourcePath: zod_1.z.string().describe('Source file path'),
        destPath: zod_1.z.string().describe('Destination file path'),
        overwrite: zod_1.z.boolean().optional().describe('Whether to overwrite existing file'),
    }),
    execute: async (params, context) => {
        const { sourcePath, destPath, overwrite } = params;
        return await file_ops_1.fileOperations.copyFile(sourcePath, destPath, context, { overwrite: overwrite ?? false });
    },
};
exports.moveFileTool = {
    name: 'move_file',
    description: 'Move a file from source to destination',
    parameters: zod_1.z.object({
        sourcePath: zod_1.z.string().describe('Source file path'),
        destPath: zod_1.z.string().describe('Destination file path'),
    }),
    execute: async (params, context) => {
        const { sourcePath, destPath } = params;
        return await file_ops_1.fileOperations.moveFile(sourcePath, destPath, context);
    },
};
exports.createDirectoryTool = {
    name: 'create_directory',
    description: 'Create a new directory',
    parameters: zod_1.z.object({
        dirPath: zod_1.z.string().describe('Path of the directory to create'),
    }),
    execute: async (params, context) => {
        const { dirPath } = params;
        return await file_ops_1.fileOperations.createDirectory(dirPath, context);
    },
};
exports.listDirectoryTool = {
    name: 'list_directory',
    description: 'List contents of a directory',
    parameters: zod_1.z.object({
        dirPath: zod_1.z.string().describe('Path of the directory to list'),
    }),
    execute: async (params, context) => {
        const { dirPath } = params;
        return await file_ops_1.fileOperations.listDirectory(dirPath, context);
    },
};
exports.searchFilesTool = {
    name: 'search_files',
    description: 'Search for files matching a pattern',
    parameters: zod_1.z.object({
        pattern: zod_1.z.string().describe('Glob pattern to search for files'),
        directory: zod_1.z.string().optional().describe('Directory to search in (default: current)'),
        includeContent: zod_1.z.boolean().optional().describe('Whether to include file content in results'),
        maxResults: zod_1.z.number().optional().describe('Maximum number of results to return'),
        fileTypes: zod_1.z.array(zod_1.z.string()).optional().describe('File extensions to filter by'),
    }),
    execute: async (params, context) => {
        const typedParams = params;
        return await file_ops_1.fileOperations.searchFiles(typedParams.pattern, context, {
            ...(typedParams.directory && { directory: typedParams.directory }),
            ...(typedParams.includeContent !== undefined && { includeContent: typedParams.includeContent }),
            ...(typedParams.maxResults !== undefined && { maxResults: typedParams.maxResults }),
            ...(typedParams.fileTypes && { fileTypes: typedParams.fileTypes }),
        });
    },
};
exports.grepFilesTool = {
    name: 'grep_files',
    description: 'Search for text content within files',
    parameters: zod_1.z.object({
        searchText: zod_1.z.string().describe('Text to search for'),
        directory: zod_1.z.string().optional().describe('Directory to search in (default: current)'),
        filePattern: zod_1.z.string().optional().describe('File pattern to search within'),
        caseSensitive: zod_1.z.boolean().optional().describe('Whether search is case sensitive'),
        maxResults: zod_1.z.number().optional().describe('Maximum number of results to return'),
        contextLines: zod_1.z.number().optional().describe('Number of context lines to include'),
    }),
    execute: async (params, context) => {
        const typedParams = params;
        return await file_ops_1.fileOperations.grepFiles(typedParams.searchText, context, {
            ...(typedParams.directory && { directory: typedParams.directory }),
            ...(typedParams.filePattern && { filePattern: typedParams.filePattern }),
            ...(typedParams.caseSensitive !== undefined && { caseSensitive: typedParams.caseSensitive }),
            ...(typedParams.maxResults !== undefined && { maxResults: typedParams.maxResults }),
            ...(typedParams.contextLines !== undefined && { contextLines: typedParams.contextLines }),
        });
    },
};
exports.getFileInfoTool = {
    name: 'get_file_info',
    description: 'Get detailed information about a file or directory',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the file or directory'),
    }),
    execute: async (params, context) => {
        const typedParams = params;
        return await file_ops_1.fileOperations.getFileInfo(typedParams.filePath, context);
    },
};
exports.setPermissionsTool = {
    name: 'set_permissions',
    description: 'Set file or directory permissions',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the file or directory'),
        permissions: zod_1.z.union([zod_1.z.string(), zod_1.z.number()]).describe('Permissions to set (e.g., "755" or 0o755)'),
    }),
    execute: async (params, context) => {
        const typedParams = params;
        return await file_ops_1.fileOperations.setPermissions(typedParams.filePath, typedParams.permissions, context);
    },
};
// Shell operation tools
exports.executeCommandTool = {
    name: 'execute_command',
    description: 'Execute a shell command',
    parameters: zod_1.z.object({
        command: zod_1.z.string().describe('Command to execute'),
        timeout: zod_1.z.number().optional().describe('Timeout in milliseconds (default: 30000)'),
        cwd: zod_1.z.string().optional().describe('Working directory for the command'),
        env: zod_1.z.record(zod_1.z.string()).optional().describe('Environment variables'),
        detached: zod_1.z.boolean().optional().describe('Whether to run command in detached mode'),
    }),
    execute: async (params, context) => {
        const typedParams = params;
        const options = {};
        if (typedParams.timeout !== undefined)
            options['timeout'] = typedParams.timeout;
        if (typedParams.cwd !== undefined)
            options['cwd'] = typedParams.cwd;
        if (typedParams.env !== undefined)
            options['env'] = typedParams.env;
        if (typedParams.detached !== undefined)
            options['detached'] = typedParams.detached;
        return await shell_ops_1.shellOperations.executeCommand(typedParams.command, context, options);
    },
};
exports.executeScriptTool = {
    name: 'execute_script',
    description: 'Execute a script with specified interpreter',
    parameters: zod_1.z.object({
        script: zod_1.z.string().describe('Script content to execute'),
        interpreter: zod_1.z.string().optional().describe('Script interpreter (default: bash)'),
        timeout: zod_1.z.number().optional().describe('Timeout in milliseconds'),
        cwd: zod_1.z.string().optional().describe('Working directory for the script'),
        env: zod_1.z.record(zod_1.z.string()).optional().describe('Environment variables'),
    }),
    execute: async (params, context) => {
        const typedParams = params;
        const options = {};
        if (typedParams.interpreter !== undefined)
            options['interpreter'] = typedParams.interpreter;
        if (typedParams.timeout !== undefined)
            options['timeout'] = typedParams.timeout;
        if (typedParams.cwd !== undefined)
            options['cwd'] = typedParams.cwd;
        if (typedParams.env !== undefined)
            options['env'] = typedParams.env;
        return await shell_ops_1.shellOperations.executeScript(typedParams.script, context, options);
    },
};
exports.killProcessTool = {
    name: 'kill_process',
    description: 'Kill a running process by PID',
    parameters: zod_1.z.object({
        pid: zod_1.z.number().describe('Process ID to kill'),
    }),
    execute: async (params, _context) => {
        const typedParams = params;
        const success = shell_ops_1.shellOperations.killProcess(typedParams.pid);
        return Promise.resolve({
            success,
            message: success
                ? `Process ${typedParams.pid} killed successfully`
                : `Failed to kill process ${typedParams.pid} or process not found`,
            data: { pid: typedParams.pid, killed: success },
        });
    },
};
exports.listProcessesTool = {
    name: 'list_processes',
    description: 'List currently running processes started by the agent',
    parameters: zod_1.z.object({}),
    execute: async (_params, _context) => {
        const processes = shell_ops_1.shellOperations.getRunningProcesses();
        return Promise.resolve({
            success: true,
            message: `Found ${processes.length} running processes`,
            data: { processes, count: processes.length },
        });
    },
};
// Context and session tools
exports.getProjectContextTool = {
    name: 'get_project_context',
    description: 'Get current project context and structure',
    parameters: zod_1.z.object({
        includeFileContent: zod_1.z.boolean().optional().describe('Whether to include file content in the response'),
        maxDepth: zod_1.z.number().optional().describe('Maximum directory depth to include'),
    }),
    execute: async (params, context) => {
        try {
            const typedParams = params;
            const { includeFileContent = false } = typedParams;
            const projectData = {
                workingDirectory: context.workingDirectory,
                projectType: context.projectContext.type,
                structure: {
                    totalFiles: context.projectContext.structure.totalFiles,
                    totalDirectories: context.projectContext.structure.totalDirectories,
                    lastIndexed: context.projectContext.structure.lastIndexed,
                },
                dependencies: context.projectContext.dependencies.slice(0, 20), // Limit to first 20
                configuration: context.projectContext.configuration,
                gitInfo: context.projectContext.gitInfo,
                environment: {
                    nodeVersion: process.version,
                    platform: process.platform,
                    arch: process.arch,
                    cwd: process.cwd(),
                },
            };
            if (includeFileContent) {
                const limitedFiles = context.projectContext.structure.files
                    .slice(0, 10)
                    .map(file => ({
                    path: file.path,
                    name: file.name,
                    size: file.size,
                    extension: file.extension,
                    lastModified: file.lastModified,
                    content: file.content?.slice(0, 1000), // Limit content
                }));
                const projectDataWithFiles = projectData;
                projectDataWithFiles.sampleFiles = limitedFiles;
            }
            return Promise.resolve({
                success: true,
                message: 'Project context retrieved successfully',
                data: projectData,
            });
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return Promise.resolve({
                success: false,
                message: 'Failed to retrieve project context',
                error: errorMessage,
            });
        }
    },
};
exports.getSessionInfoTool = {
    name: 'get_session_info',
    description: 'Get current session information and statistics',
    parameters: zod_1.z.object({
        includeMessages: zod_1.z.boolean().optional().describe('Whether to include recent messages'),
        messageLimit: zod_1.z.number().optional().describe('Number of recent messages to include'),
    }),
    execute: async (params, context) => {
        try {
            const typedParams = params;
            const { includeMessages = false, messageLimit = 5 } = typedParams;
            const sessionData = {
                sessionId: context.sessionId,
                workingDirectory: context.workingDirectory,
                environmentVariables: Object.keys(context.environment).length,
                projectType: context.projectContext.type,
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
                timestamp: new Date().toISOString(),
            };
            if (includeMessages) {
                const { sessionManager } = await Promise.resolve().then(() => __importStar(require('../session/manager')));
                const messages = sessionManager.getMessages();
                const sessionDataWithMessages = sessionData;
                sessionDataWithMessages.recentMessages = messages
                    .slice(-messageLimit)
                    .map(msg => ({
                    role: msg.role,
                    content: msg.content.slice(0, 200), // Truncate content
                    timestamp: new Date().toISOString(),
                }));
            }
            return {
                success: true,
                message: 'Session information retrieved successfully',
                data: sessionData,
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                message: 'Failed to retrieve session information',
                error: errorMessage,
            };
        }
    },
};
// Advanced tools for enhanced functionality
exports.watchDirectoryTool = {
    name: 'watch_directory',
    description: 'Start watching a directory for file changes',
    parameters: zod_1.z.object({
        directory: zod_1.z.string().describe('Directory to watch'),
        patterns: zod_1.z.array(zod_1.z.string()).optional().describe('File patterns to watch'),
        ignorePatterns: zod_1.z.array(zod_1.z.string()).optional().describe('Patterns to ignore'),
    }),
    execute: async (params, _context) => {
        try {
            const typedParams = params;
            const { directory, patterns = ['**/*'], ignorePatterns = [] } = typedParams;
            const chokidar = await Promise.resolve().then(() => __importStar(require('chokidar')));
            // Start watching the directory
            const watcher = chokidar.watch(directory, {
                ignored: ignorePatterns,
                persistent: true,
                ignoreInitial: true,
            });
            const watcherId = `watcher_${Date.now()}`;
            watcher.on('all', (event, path) => {
                logger_1.logger.debug(`File ${event}: ${path}`, undefined, 'FileWatcher');
            });
            return {
                success: true,
                message: `Started watching directory: ${directory}`,
                data: {
                    directory,
                    patterns,
                    ignorePatterns,
                    watcherId,
                    status: 'active',
                },
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                message: `Failed to watch directory: ${params.directory ?? 'unknown'}`,
                error: errorMessage,
            };
        }
    },
};
exports.analyzeCodeTool = {
    name: 'analyze_code',
    description: 'Analyze code files for patterns, complexity, and issues',
    parameters: zod_1.z.object({
        filePath: zod_1.z.string().describe('Path to the code file to analyze'),
        analysisType: zod_1.z.enum(['complexity', 'patterns', 'dependencies', 'all']).optional().describe('Type of analysis to perform'),
    }),
    execute: async (params, context) => {
        try {
            const typedParams = params;
            const { filePath, analysisType = 'all' } = typedParams;
            // Handle empty parameters for testing
            if (!filePath) {
                return {
                    success: false,
                    message: 'File path is required for code analysis',
                    error: 'MISSING_FILE_PATH',
                };
            }
            const fileResult = await file_ops_1.fileOperations.readFile(filePath, context);
            if (!fileResult.success) {
                return fileResult;
            }
            const fileData = fileResult.data;
            const content = typeof fileData?.content === 'string' ? fileData.content : '';
            const analysis = {
                file: filePath,
                size: content.length,
                lines: content.split('\n').length,
                timestamp: new Date().toISOString(),
            };
            if (analysisType === 'complexity' || analysisType === 'all') {
                analysis.complexity = {
                    cyclomaticComplexity: calculateCyclomaticComplexity(content),
                    nestingDepth: calculateNestingDepth(content),
                    functionCount: (content.match(/function\s+\w+|=>\s*{|class\s+\w+/g) ?? []).length,
                };
            }
            if (analysisType === 'patterns' || analysisType === 'all') {
                analysis.patterns = {
                    todoComments: (content.match(/\/\/\s*TODO|\/\*\s*TODO|#\s*TODO/gi) ?? []).length,
                    fixmeComments: (content.match(/\/\/\s*FIXME|\/\*\s*FIXME|#\s*FIXME/gi) ?? []).length,
                    consoleStatements: (content.match(/console\.(log|error|warn|info)/g) ?? []).length,
                    debugStatements: (content.match(/debugger|console\.debug/g) ?? []).length,
                };
            }
            if (analysisType === 'dependencies' || analysisType === 'all') {
                analysis.dependencies = {
                    imports: (content.match(/import\s+.*from|require\s*\(/g) ?? []).length,
                    exports: (content.match(/export\s+|module\.exports/g) ?? []).length,
                };
            }
            return {
                success: true,
                message: `Code analysis completed for: ${filePath}`,
                data: analysis,
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                message: `Failed to analyze code: ${params.filePath ?? 'unknown file'}`,
                error: errorMessage,
            };
        }
    },
};
// Helper functions for code analysis
function calculateCyclomaticComplexity(content) {
    const complexityKeywords = [
        'if', 'else', 'while', 'for', 'switch', 'case', 'catch', 'try',
        'break', 'continue', 'return'
    ];
    // Special operators that need different handling
    const operatorPatterns = [
        /&&/g, // logical AND
        /\|\|/g, // logical OR
        /\?/g, // ternary operator
    ];
    let complexity = 1; // Base complexity
    // Count keyword-based complexity
    for (const keyword of complexityKeywords) {
        const matches = content.match(new RegExp(`\\b${keyword}\\b`, 'g'));
        if (matches) {
            complexity += matches.length;
        }
    }
    // Count operator-based complexity
    for (const pattern of operatorPatterns) {
        const matches = content.match(pattern);
        if (matches) {
            complexity += matches.length;
        }
    }
    return complexity;
}
function calculateNestingDepth(content) {
    let maxDepth = 0;
    let currentDepth = 0;
    for (const char of content) {
        if (char === '{') {
            currentDepth++;
            maxDepth = Math.max(maxDepth, currentDepth);
        }
        else if (char === '}') {
            currentDepth--;
        }
    }
    return maxDepth;
}
exports.executeParallelToolsTool = {
    name: 'execute_parallel_tools',
    description: 'Execute multiple tools in parallel for improved performance',
    parameters: zod_1.z.object({
        toolCalls: zod_1.z.array(zod_1.z.object({
            toolName: zod_1.z.string().describe('Name of the tool to execute'),
            parameters: zod_1.z.record(zod_1.z.any()).describe('Parameters for the tool'),
        })).describe('Array of tool calls to execute in parallel'),
        maxConcurrency: zod_1.z.number().optional().describe('Maximum number of concurrent executions'),
    }),
    execute: async (params, context) => {
        try {
            const typedParams = params;
            const { toolCalls, maxConcurrency = 3 } = typedParams;
            const results = [];
            // Execute tools in batches to respect concurrency limit
            for (let i = 0; i < toolCalls.length; i += maxConcurrency) {
                const batch = toolCalls.slice(i, i + maxConcurrency);
                const batchPromises = batch.map(async (toolCall) => {
                    try {
                        const tool = exports.toolRegistry.getTool(toolCall.toolName);
                        if (!tool) {
                            return {
                                toolName: toolCall.toolName,
                                success: false,
                                error: 'Tool not found',
                            };
                        }
                        const result = await tool.execute(toolCall.parameters, context);
                        return {
                            toolName: toolCall.toolName,
                            ...result,
                        };
                    }
                    catch (error) {
                        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                        return {
                            toolName: toolCall.toolName,
                            success: false,
                            error: errorMessage,
                        };
                    }
                });
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            }
            const successCount = results.filter(r => r.success).length;
            const failureCount = results.length - successCount;
            return {
                success: true,
                message: `Executed ${toolCalls.length} tools in parallel. ${successCount} succeeded, ${failureCount} failed.`,
                data: {
                    results,
                    summary: {
                        total: toolCalls.length,
                        successful: successCount,
                        failed: failureCount,
                        executionTime: Date.now(),
                    },
                },
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                message: 'Failed to execute parallel tools',
                error: errorMessage,
            };
        }
    },
};
// Network and system tools
exports.networkRequestTool = {
    name: 'network_request',
    description: 'Make HTTP requests to external APIs or services',
    parameters: zod_1.z.object({
        url: zod_1.z.string().describe('URL to make the request to'),
        method: zod_1.z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).optional().describe('HTTP method'),
        headers: zod_1.z.record(zod_1.z.string()).optional().describe('Request headers'),
        body: zod_1.z.string().optional().describe('Request body (for POST/PUT/PATCH)'),
        timeout: zod_1.z.number().optional().describe('Request timeout in milliseconds'),
    }),
    execute: async (params, _context) => {
        try {
            const typedParams = params;
            const { url, method = 'GET', headers = {}, body, timeout = 10000 } = typedParams;
            // Check if network access is allowed
            const cliConfig = config_1.config.getConfig();
            if (!cliConfig.tools.allowNetworkAccess) {
                return {
                    success: false,
                    message: 'Network access is disabled in configuration',
                    error: 'NETWORK_DISABLED',
                };
            }
            const axios = await Promise.resolve().then(() => __importStar(require('axios')));
            const axiosConfig = {
                url,
                method,
                headers,
                timeout,
                validateStatus: () => true, // Don't throw on HTTP error status
            };
            if (body !== undefined) {
                axiosConfig.data = body;
            }
            const response = await axios.default(axiosConfig);
            return {
                success: true,
                message: `HTTP ${method} request to ${url} completed`,
                data: {
                    status: response.status,
                    statusText: response.statusText,
                    headers: response.headers,
                    data: response.data,
                    url: response.config.url,
                },
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorCode = error instanceof Error && 'code' in error ? error.code : undefined;
            return {
                success: false,
                message: `Network request failed: ${errorMessage}`,
                error: errorCode ?? 'NETWORK_ERROR',
            };
        }
    },
};
exports.systemInfoTool = {
    name: 'get_system_info',
    description: 'Get detailed system information and environment details',
    parameters: zod_1.z.object({
        includeEnv: zod_1.z.boolean().optional().describe('Whether to include environment variables'),
        includeProcesses: zod_1.z.boolean().optional().describe('Whether to include running processes info'),
    }),
    execute: async (params, _context) => {
        try {
            const typedParams = params;
            const { includeEnv = false, includeProcesses = false } = typedParams;
            const os = await Promise.resolve().then(() => __importStar(require('os')));
            const systemInfo = {
                platform: process.platform,
                arch: process.arch,
                nodeVersion: process.version,
                uptime: process.uptime(),
                memory: {
                    total: os.totalmem(),
                    free: os.freemem(),
                    used: os.totalmem() - os.freemem(),
                    processUsage: process.memoryUsage(),
                },
                cpu: {
                    model: os.cpus()[0]?.model ?? 'Unknown',
                    cores: os.cpus().length,
                    loadAverage: os.loadavg(),
                },
                network: os.networkInterfaces(),
                hostname: os.hostname(),
                userInfo: os.userInfo(),
                tmpdir: os.tmpdir(),
                homedir: os.homedir(),
            };
            if (includeEnv) {
                systemInfo.environment = process.env;
            }
            if (includeProcesses) {
                systemInfo.process = {
                    pid: process.pid,
                    ppid: process.ppid,
                    title: process.title,
                    argv: process.argv,
                    execPath: process.execPath,
                    cwd: process.cwd(),
                };
            }
            return {
                success: true,
                message: 'System information retrieved successfully',
                data: systemInfo,
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                message: 'Failed to retrieve system information',
                error: errorMessage,
            };
        }
    },
};
exports.validateJsonTool = {
    name: 'validate_json',
    description: 'Validate and format JSON content',
    parameters: zod_1.z.object({
        jsonContent: zod_1.z.string().describe('JSON content to validate'),
        format: zod_1.z.boolean().optional().describe('Whether to format the JSON'),
    }),
    execute: async (params, _context) => {
        try {
            const typedParams = params;
            const { jsonContent, format = false } = typedParams;
            // Parse JSON to validate
            const parsed = JSON.parse(jsonContent);
            const result = {
                valid: true,
                parsed,
            };
            if (format) {
                result.formatted = JSON.stringify(parsed, null, 2);
            }
            return Promise.resolve({
                success: true,
                message: 'JSON validation successful',
                data: result,
            });
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const positionMatch = errorMessage.match(/position (\d+)/);
            return Promise.resolve({
                success: false,
                message: 'JSON validation failed',
                data: {
                    valid: false,
                    error: errorMessage,
                    position: positionMatch?.[1],
                },
            });
        }
    },
};
exports.manageEnvironmentTool = {
    name: 'manage_environment',
    description: 'Get, set, or unset environment variables',
    parameters: zod_1.z.object({
        action: zod_1.z.enum(['get', 'set', 'unset', 'list']).describe('Action to perform'),
        variable: zod_1.z.string().optional().describe('Environment variable name'),
        value: zod_1.z.string().optional().describe('Value to set (for set action)'),
        pattern: zod_1.z.string().optional().describe('Pattern to filter variables (for list action)'),
    }),
    execute: async (params, context) => {
        try {
            const typedParams = params;
            const { action, variable, value, pattern } = typedParams;
            switch (action) {
                case 'get': {
                    if (!variable) {
                        return Promise.resolve({
                            success: false,
                            message: 'Variable name is required for get action',
                            error: 'MISSING_VARIABLE',
                        });
                    }
                    const envValue = context.environment[variable];
                    return Promise.resolve({
                        success: true,
                        message: `Environment variable retrieved: ${variable}`,
                        data: {
                            variable,
                            value: envValue ?? null,
                            exists: envValue !== undefined,
                        },
                    });
                }
                case 'set': {
                    if (!variable || value === undefined) {
                        return Promise.resolve({
                            success: false,
                            message: 'Variable name and value are required for set action',
                            error: 'MISSING_PARAMETERS',
                        });
                    }
                    // Update context environment
                    context.environment[variable] = value;
                    // Also set in process environment for current session
                    process.env[variable] = value;
                    return Promise.resolve({
                        success: true,
                        message: `Environment variable set: ${variable}`,
                        data: {
                            variable,
                            value,
                            action: 'set',
                        },
                    });
                }
                case 'unset': {
                    if (!variable) {
                        return Promise.resolve({
                            success: false,
                            message: 'Variable name is required for unset action',
                            error: 'MISSING_VARIABLE',
                        });
                    }
                    const existed = variable in context.environment;
                    delete context.environment[variable];
                    delete process.env[variable];
                    return Promise.resolve({
                        success: true,
                        message: `Environment variable unset: ${variable}`,
                        data: {
                            variable,
                            existed,
                            action: 'unset',
                        },
                    });
                }
                case 'list': {
                    let variables = Object.entries(context.environment);
                    if (pattern) {
                        const regex = new RegExp(pattern, 'i');
                        variables = variables.filter(([key]) => regex.test(key));
                    }
                    return Promise.resolve({
                        success: true,
                        message: `Listed ${variables.length} environment variables`,
                        data: {
                            variables: variables.reduce((acc, [key, val]) => {
                                acc[key] = val;
                                return acc;
                            }, {}),
                            count: variables.length,
                            pattern: pattern ?? null,
                        },
                    });
                }
                default: {
                    const neverAction = action;
                    return Promise.resolve({
                        success: false,
                        message: `Unknown action: ${String(neverAction)}`,
                        error: 'INVALID_ACTION',
                    });
                }
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return Promise.resolve({
                success: false,
                message: 'Failed to manage environment variable',
                error: errorMessage,
            });
        }
    },
};
exports.monitorSystemTool = {
    name: 'monitor_system',
    description: 'Monitor system resources and performance metrics',
    parameters: zod_1.z.object({
        duration: zod_1.z.number().optional().describe('Monitoring duration in seconds (default: 5)'),
        interval: zod_1.z.number().optional().describe('Sampling interval in seconds (default: 1)'),
        metrics: zod_1.z.array(zod_1.z.enum(['cpu', 'memory', 'disk', 'network'])).optional().describe('Metrics to monitor'),
    }),
    execute: async (params, _context) => {
        try {
            const typedParams = params;
            const { duration = 5, interval = 1, metrics = ['cpu', 'memory'] } = typedParams;
            const os = await Promise.resolve().then(() => __importStar(require('os')));
            const samples = [];
            const startTime = Date.now();
            for (let i = 0; i < duration; i++) {
                const sample = {
                    timestamp: new Date().toISOString(),
                    uptime: process.uptime(),
                };
                if (metrics.includes('cpu')) {
                    const cpus = os.cpus();
                    sample.cpu = {
                        cores: cpus.length,
                        model: cpus[0]?.model ?? 'Unknown',
                        loadAverage: os.loadavg(),
                    };
                }
                if (metrics.includes('memory')) {
                    const totalMem = os.totalmem();
                    const freeMem = os.freemem();
                    const processMemory = process.memoryUsage();
                    sample.memory = {
                        total: totalMem,
                        free: freeMem,
                        used: totalMem - freeMem,
                        usagePercent: ((totalMem - freeMem) / totalMem) * 100,
                        process: processMemory,
                    };
                }
                if (metrics.includes('disk')) {
                    // Basic disk info - would need additional libraries for detailed disk monitoring
                    sample.disk = {
                        cwd: process.cwd(),
                        // Note: Real disk usage would require platform-specific commands
                    };
                }
                if (metrics.includes('network')) {
                    sample.network = {
                        interfaces: Object.keys(os.networkInterfaces()),
                        // Note: Real network monitoring would require additional implementation
                    };
                }
                samples.push(sample);
                if (i < duration - 1) {
                    await new Promise(resolve => setTimeout(resolve, interval * 1000));
                }
            }
            const endTime = Date.now();
            return {
                success: true,
                message: `System monitoring completed for ${duration} seconds`,
                data: {
                    duration: (endTime - startTime) / 1000,
                    samples,
                    summary: {
                        sampleCount: samples.length,
                        metrics,
                        interval,
                    },
                },
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                message: 'Failed to monitor system',
                error: errorMessage,
            };
        }
    },
};
exports.manageDependenciesTool = {
    name: 'manage_dependencies',
    description: 'Install, update, or remove project dependencies',
    parameters: zod_1.z.object({
        action: zod_1.z.enum(['install', 'update', 'remove', 'list', 'audit']).describe('Action to perform'),
        packages: zod_1.z.array(zod_1.z.string()).optional().describe('Package names for install/update/remove'),
        packageManager: zod_1.z.enum(['npm', 'yarn', 'pnpm', 'pip', 'cargo', 'go']).optional().describe('Package manager to use'),
        dev: zod_1.z.boolean().optional().describe('Install as dev dependency'),
        global: zod_1.z.boolean().optional().describe('Install globally'),
    }),
    execute: async (params, context) => {
        try {
            const typedParams = params;
            const { action, packages = [], packageManager, dev = false, global = false } = typedParams;
            // Detect package manager if not specified
            let pm = packageManager;
            if (!pm) {
                const projectType = context.projectContext.type;
                const config = context.projectContext.configuration;
                if (projectType === 'nodejs' || projectType === 'web') {
                    pm = config.packageManager ?? 'npm';
                }
                else if (projectType === 'python') {
                    pm = 'pip';
                }
                else if (projectType === 'rust') {
                    pm = 'cargo';
                }
                else if (projectType === 'go') {
                    pm = 'go';
                }
                else {
                    return {
                        success: false,
                        message: 'Cannot determine package manager for this project type',
                        error: 'UNKNOWN_PACKAGE_MANAGER',
                    };
                }
            }
            // Build command based on action and package manager
            let command = '';
            switch (pm) {
                case 'npm':
                    switch (action) {
                        case 'install':
                            command = `npm install${global ? ' -g' : ''}${dev ? ' --save-dev' : ''} ${packages.join(' ')}`;
                            break;
                        case 'update':
                            command = packages.length > 0 ? `npm update ${packages.join(' ')}` : 'npm update';
                            break;
                        case 'remove':
                            command = `npm uninstall${global ? ' -g' : ''} ${packages.join(' ')}`;
                            break;
                        case 'list':
                            command = `npm list${global ? ' -g' : ''} --depth=0`;
                            break;
                        case 'audit':
                            command = 'npm audit';
                            break;
                    }
                    break;
                case 'yarn':
                    switch (action) {
                        case 'install':
                            command = packages.length > 0
                                ? `yarn add${dev ? ' --dev' : ''} ${packages.join(' ')}`
                                : 'yarn install';
                            break;
                        case 'update':
                            command = packages.length > 0 ? `yarn upgrade ${packages.join(' ')}` : 'yarn upgrade';
                            break;
                        case 'remove':
                            command = `yarn remove ${packages.join(' ')}`;
                            break;
                        case 'list':
                            command = 'yarn list --depth=0';
                            break;
                        case 'audit':
                            command = 'yarn audit';
                            break;
                    }
                    break;
                case 'pip':
                    switch (action) {
                        case 'install':
                            command = `pip install ${packages.join(' ')}`;
                            break;
                        case 'update':
                            command = packages.length > 0
                                ? `pip install --upgrade ${packages.join(' ')}`
                                : 'pip list --outdated';
                            break;
                        case 'remove':
                            command = `pip uninstall -y ${packages.join(' ')}`;
                            break;
                        case 'list':
                            command = 'pip list';
                            break;
                        case 'audit':
                            command = 'pip check';
                            break;
                    }
                    break;
                default:
                    return {
                        success: false,
                        message: `Package manager ${pm} not fully supported yet`,
                        error: 'UNSUPPORTED_PACKAGE_MANAGER',
                    };
            }
            if (!command) {
                return {
                    success: false,
                    message: `Invalid action ${action} for package manager ${pm}`,
                    error: 'INVALID_ACTION',
                };
            }
            // Execute the command using shell operations
            const { shellOperations } = await Promise.resolve().then(() => __importStar(require('../operations/shell-ops')));
            const result = await shellOperations.executeCommand(command, context, {
                timeout: 120000, // 2 minutes timeout for package operations
            });
            const response = {
                success: result.success,
                message: result.success
                    ? `Package ${action} completed successfully`
                    : `Package ${action} failed`,
                data: {
                    action,
                    packages,
                    packageManager: pm,
                    command,
                    output: result.data,
                },
            };
            if (!result.success && result.error) {
                response.error = result.error;
            }
            return response;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                message: 'Failed to manage dependencies',
                error: errorMessage,
            };
        }
    },
};
// Tool registry
exports.allTools = [
    // File operations
    exports.readFileTool,
    exports.writeFileTool,
    exports.deleteFileTool,
    exports.copyFileTool,
    exports.moveFileTool,
    exports.createDirectoryTool,
    exports.listDirectoryTool,
    exports.searchFilesTool,
    exports.grepFilesTool,
    exports.getFileInfoTool,
    exports.setPermissionsTool,
    // Shell operations
    exports.executeCommandTool,
    exports.executeScriptTool,
    exports.killProcessTool,
    exports.listProcessesTool,
    // Context operations
    exports.getProjectContextTool,
    exports.getSessionInfoTool,
    // Advanced operations
    exports.watchDirectoryTool,
    exports.analyzeCodeTool,
    exports.executeParallelToolsTool,
    // Network and system operations
    exports.networkRequestTool,
    exports.systemInfoTool,
    exports.validateJsonTool,
    exports.manageEnvironmentTool,
    exports.monitorSystemTool,
    exports.manageDependenciesTool,
];
class ToolRegistry {
    tools = new Map();
    constructor() {
        this.registerTools(exports.allTools);
    }
    registerTool(tool) {
        this.tools.set(tool.name, tool);
        logger_1.logger.debug(`Tool registered: ${tool.name}`, undefined, 'ToolRegistry');
    }
    registerTools(tools) {
        tools.forEach(tool => this.registerTool(tool));
    }
    getTool(name) {
        return this.tools.get(name);
    }
    getAllTools() {
        return Array.from(this.tools.values());
    }
    getToolNames() {
        return Array.from(this.tools.keys());
    }
    hasTools(name) {
        return this.tools.has(name);
    }
    getToolsByCategory(category) {
        return this.getAllTools().filter(tool => tool.name.startsWith(category) || tool.description.toLowerCase().includes(category));
    }
}
exports.ToolRegistry = ToolRegistry;
exports.toolRegistry = new ToolRegistry();
//# sourceMappingURL=index.js.map