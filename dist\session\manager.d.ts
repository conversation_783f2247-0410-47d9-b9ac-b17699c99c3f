import { Session, AgentMessage, SessionStats } from '../types';
export declare class SessionManager {
    private static instance;
    private currentSession;
    private sessionsDir;
    private constructor();
    static getInstance(): SessionManager;
    createSession(name?: string, workingDirectory?: string): Promise<Session>;
    loadSession(sessionId: string): Promise<Session>;
    private restoreSessionContext;
    saveSession(session: Session): Promise<void>;
    deleteSession(sessionId: string): Promise<void>;
    listSessions(): Promise<Session[]>;
    getCurrentSession(): Session | null;
    addMessage(message: AgentMessage): Promise<void>;
    getMessages(): AgentMessage[];
    refreshSessionContext(session: Session): Promise<void>;
    private shouldRefreshContext;
    private serializeSession;
    private deserializeSession;
    exportSession(sessionId: string, outputPath: string): Promise<void>;
    importSession(importPath: string): Promise<Session>;
    cleanupOldSessions(maxAge?: number): Promise<void>;
    getSessionStats(sessionId?: string): Promise<SessionStats>;
    optimizeSession(sessionId?: string): Promise<void>;
    backupSession(sessionId?: string): Promise<string>;
}
export declare const sessionManager: SessionManager;
//# sourceMappingURL=manager.d.ts.map