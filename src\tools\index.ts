import { z } from 'zod';
import { Tool } from '@/types';
import { fileOperations } from '@/operations/file-ops';
import { shellOperations } from '@/operations/shell-ops';
import { logger } from '@/utils/logger';
import { config } from '@/config';

// File operation tools
export const readFileTool: Tool = {
  name: 'read_file',
  description: 'Read the contents of a file',
  parameters: z.object({
    filePath: z.string().describe('Path to the file to read'),
  }),
  execute: async (params, context) => {
    const { filePath } = params as { filePath: string };
    return await fileOperations.readFile(filePath, context);
  },
};

export const writeFileTool: Tool = {
  name: 'write_file',
  description: 'Write content to a file',
  parameters: z.object({
    filePath: z.string().describe('Path to the file to write'),
    content: z.string().describe('Content to write to the file'),
    overwrite: z.boolean().optional().describe('Whether to overwrite existing file'),
    createDirs: z.boolean().optional().describe('Whether to create parent directories'),
  }),
  execute: async (params, context) => {
    const { filePath, content, overwrite, createDirs } = params as {
      filePath: string;
      content: string;
      overwrite?: boolean;
      createDirs?: boolean;
    };
    return await fileOperations.writeFile(
      filePath,
      content,
      context,
      {
        overwrite: overwrite ?? false,
        createDirs: createDirs ?? true
      }
    );
  },
};

export const deleteFileTool: Tool = {
  name: 'delete_file',
  description: 'Delete a file or directory',
  parameters: z.object({
    filePath: z.string().describe('Path to the file or directory to delete'),
  }),
  execute: async (params, context) => {
    const { filePath } = params as { filePath: string };
    return await fileOperations.deleteFile(filePath, context);
  },
};

export const copyFileTool: Tool = {
  name: 'copy_file',
  description: 'Copy a file from source to destination',
  parameters: z.object({
    sourcePath: z.string().describe('Source file path'),
    destPath: z.string().describe('Destination file path'),
    overwrite: z.boolean().optional().describe('Whether to overwrite existing file'),
  }),
  execute: async (params, context) => {
    const { sourcePath, destPath, overwrite } = params as {
      sourcePath: string;
      destPath: string;
      overwrite?: boolean;
    };
    return await fileOperations.copyFile(
      sourcePath,
      destPath,
      context,
      { overwrite: overwrite ?? false }
    );
  },
};

export const moveFileTool: Tool = {
  name: 'move_file',
  description: 'Move a file from source to destination',
  parameters: z.object({
    sourcePath: z.string().describe('Source file path'),
    destPath: z.string().describe('Destination file path'),
  }),
  execute: async (params, context) => {
    const { sourcePath, destPath } = params as { sourcePath: string; destPath: string };
    return await fileOperations.moveFile(sourcePath, destPath, context);
  },
};

export const createDirectoryTool: Tool = {
  name: 'create_directory',
  description: 'Create a new directory',
  parameters: z.object({
    dirPath: z.string().describe('Path of the directory to create'),
  }),
  execute: async (params, context) => {
    const { dirPath } = params as { dirPath: string };
    return await fileOperations.createDirectory(dirPath, context);
  },
};

export const listDirectoryTool: Tool = {
  name: 'list_directory',
  description: 'List contents of a directory',
  parameters: z.object({
    dirPath: z.string().describe('Path of the directory to list'),
  }),
  execute: async (params, context) => {
    const { dirPath } = params as { dirPath: string };
    return await fileOperations.listDirectory(dirPath, context);
  },
};

export const searchFilesTool: Tool = {
  name: 'search_files',
  description: 'Search for files matching a pattern',
  parameters: z.object({
    pattern: z.string().describe('Glob pattern to search for files'),
    directory: z.string().optional().describe('Directory to search in (default: current)'),
    includeContent: z.boolean().optional().describe('Whether to include file content in results'),
    maxResults: z.number().optional().describe('Maximum number of results to return'),
    fileTypes: z.array(z.string()).optional().describe('File extensions to filter by'),
  }),
  execute: async (params: unknown, context) => {
    const typedParams = params as { pattern: string; directory?: string; includeContent?: boolean; maxResults?: number; fileTypes?: string[] };
    return await fileOperations.searchFiles(typedParams.pattern, context, {
      ...(typedParams.directory && { directory: typedParams.directory }),
      ...(typedParams.includeContent !== undefined && { includeContent: typedParams.includeContent }),
      ...(typedParams.maxResults !== undefined && { maxResults: typedParams.maxResults }),
      ...(typedParams.fileTypes && { fileTypes: typedParams.fileTypes }),
    });
  },
};

export const grepFilesTool: Tool = {
  name: 'grep_files',
  description: 'Search for text content within files',
  parameters: z.object({
    searchText: z.string().describe('Text to search for'),
    directory: z.string().optional().describe('Directory to search in (default: current)'),
    filePattern: z.string().optional().describe('File pattern to search within'),
    caseSensitive: z.boolean().optional().describe('Whether search is case sensitive'),
    maxResults: z.number().optional().describe('Maximum number of results to return'),
    contextLines: z.number().optional().describe('Number of context lines to include'),
  }),
  execute: async (params: unknown, context) => {
    const typedParams = params as { searchText: string; directory?: string; filePattern?: string; caseSensitive?: boolean; maxResults?: number; contextLines?: number };
    return await fileOperations.grepFiles(typedParams.searchText, context, {
      ...(typedParams.directory && { directory: typedParams.directory }),
      ...(typedParams.filePattern && { filePattern: typedParams.filePattern }),
      ...(typedParams.caseSensitive !== undefined && { caseSensitive: typedParams.caseSensitive }),
      ...(typedParams.maxResults !== undefined && { maxResults: typedParams.maxResults }),
      ...(typedParams.contextLines !== undefined && { contextLines: typedParams.contextLines }),
    });
  },
};

export const getFileInfoTool: Tool = {
  name: 'get_file_info',
  description: 'Get detailed information about a file or directory',
  parameters: z.object({
    filePath: z.string().describe('Path to the file or directory'),
  }),
  execute: async (params: unknown, context) => {
    const typedParams = params as { filePath: string };
    return await fileOperations.getFileInfo(typedParams.filePath, context);
  },
};

export const setPermissionsTool: Tool = {
  name: 'set_permissions',
  description: 'Set file or directory permissions',
  parameters: z.object({
    filePath: z.string().describe('Path to the file or directory'),
    permissions: z.union([z.string(), z.number()]).describe('Permissions to set (e.g., "755" or 0o755)'),
  }),
  execute: async (params: unknown, context) => {
    const typedParams = params as { filePath: string; permissions: string | number };
    return await fileOperations.setPermissions(typedParams.filePath, typedParams.permissions, context);
  },
};

// Shell operation tools
export const executeCommandTool: Tool = {
  name: 'execute_command',
  description: 'Execute a shell command',
  parameters: z.object({
    command: z.string().describe('Command to execute'),
    timeout: z.number().optional().describe('Timeout in milliseconds (default: 30000)'),
    cwd: z.string().optional().describe('Working directory for the command'),
    env: z.record(z.string()).optional().describe('Environment variables'),
    detached: z.boolean().optional().describe('Whether to run command in detached mode'),
  }),
  execute: async (params, context) => {
    const typedParams = params as {
      command: string;
      timeout?: number;
      cwd?: string;
      env?: Record<string, string>;
      detached?: boolean;
    };
    const options: Record<string, unknown> = {};
    if (typedParams.timeout !== undefined) options['timeout'] = typedParams.timeout;
    if (typedParams.cwd !== undefined) options['cwd'] = typedParams.cwd;
    if (typedParams.env !== undefined) options['env'] = typedParams.env;
    if (typedParams.detached !== undefined) options['detached'] = typedParams.detached;

    return await shellOperations.executeCommand(typedParams.command, context, options);
  },
};

export const executeScriptTool: Tool = {
  name: 'execute_script',
  description: 'Execute a script with specified interpreter',
  parameters: z.object({
    script: z.string().describe('Script content to execute'),
    interpreter: z.string().optional().describe('Script interpreter (default: bash)'),
    timeout: z.number().optional().describe('Timeout in milliseconds'),
    cwd: z.string().optional().describe('Working directory for the script'),
    env: z.record(z.string()).optional().describe('Environment variables'),
  }),
  execute: async (params, context) => {
    const typedParams = params as {
      script: string;
      interpreter?: string;
      timeout?: number;
      cwd?: string;
      env?: Record<string, string>;
    };
    const options: Record<string, unknown> = {};
    if (typedParams.interpreter !== undefined) options['interpreter'] = typedParams.interpreter;
    if (typedParams.timeout !== undefined) options['timeout'] = typedParams.timeout;
    if (typedParams.cwd !== undefined) options['cwd'] = typedParams.cwd;
    if (typedParams.env !== undefined) options['env'] = typedParams.env;

    return await shellOperations.executeScript(typedParams.script, context, options);
  },
};

export const killProcessTool: Tool = {
  name: 'kill_process',
  description: 'Kill a running process by PID',
  parameters: z.object({
    pid: z.number().describe('Process ID to kill'),
  }),
  execute: async (params, _context) => {
    const typedParams = params as { pid: number };
    const success = shellOperations.killProcess(typedParams.pid);
    return Promise.resolve({
      success,
      message: success
        ? `Process ${typedParams.pid} killed successfully`
        : `Failed to kill process ${typedParams.pid} or process not found`,
      data: { pid: typedParams.pid, killed: success },
    });
  },
};

export const listProcessesTool: Tool = {
  name: 'list_processes',
  description: 'List currently running processes started by the agent',
  parameters: z.object({}),
  execute: async (_params, _context) => {
    const processes = shellOperations.getRunningProcesses();
    return Promise.resolve({
      success: true,
      message: `Found ${processes.length} running processes`,
      data: { processes, count: processes.length },
    });
  },
};

// Context and session tools
export const getProjectContextTool: Tool = {
  name: 'get_project_context',
  description: 'Get current project context and structure',
  parameters: z.object({
    includeFileContent: z.boolean().optional().describe('Whether to include file content in the response'),
    maxDepth: z.number().optional().describe('Maximum directory depth to include'),
  }),
  execute: async (params, context) => {
    try {
      const typedParams = params as { includeFileContent?: boolean; maxDepth?: number };
      const { includeFileContent = false } = typedParams;

      const projectData = {
        workingDirectory: context.workingDirectory,
        projectType: context.projectContext.type,
        structure: {
          totalFiles: context.projectContext.structure.totalFiles,
          totalDirectories: context.projectContext.structure.totalDirectories,
          lastIndexed: context.projectContext.structure.lastIndexed,
        },
        dependencies: context.projectContext.dependencies.slice(0, 20), // Limit to first 20
        configuration: context.projectContext.configuration,
        gitInfo: context.projectContext.gitInfo,
        environment: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
          cwd: process.cwd(),
        },
      };

      if (includeFileContent) {
        const limitedFiles = context.projectContext.structure.files
          .slice(0, 10)
          .map(file => ({
            path: file.path,
            name: file.name,
            size: file.size,
            extension: file.extension,
            lastModified: file.lastModified,
            content: file.content?.slice(0, 1000), // Limit content
          }));
        const projectDataWithFiles = projectData as typeof projectData & { sampleFiles: typeof limitedFiles };
        projectDataWithFiles.sampleFiles = limitedFiles;
      }

      return Promise.resolve({
        success: true,
        message: 'Project context retrieved successfully',
        data: projectData,
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return Promise.resolve({
        success: false,
        message: 'Failed to retrieve project context',
        error: errorMessage,
      });
    }
  },
};

export const getSessionInfoTool: Tool = {
  name: 'get_session_info',
  description: 'Get current session information and statistics',
  parameters: z.object({
    includeMessages: z.boolean().optional().describe('Whether to include recent messages'),
    messageLimit: z.number().optional().describe('Number of recent messages to include'),
  }),
  execute: async (params, context) => {
    try {
      const typedParams = params as { includeMessages?: boolean; messageLimit?: number };
      const { includeMessages = false, messageLimit = 5 } = typedParams;

      const sessionData = {
        sessionId: context.sessionId,
        workingDirectory: context.workingDirectory,
        environmentVariables: Object.keys(context.environment).length,
        projectType: context.projectContext.type,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        timestamp: new Date().toISOString(),
      };

      if (includeMessages) {
        const { sessionManager } = await import('@/session/manager');
        const messages = sessionManager.getMessages();
        const sessionDataWithMessages = sessionData as typeof sessionData & { recentMessages: unknown[] };
        sessionDataWithMessages.recentMessages = messages
          .slice(-messageLimit)
          .map(msg => ({
            role: msg.role,
            content: msg.content.slice(0, 200), // Truncate content
            timestamp: new Date().toISOString(),
          }));
      }

      return {
        success: true,
        message: 'Session information retrieved successfully',
        data: sessionData,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        message: 'Failed to retrieve session information',
        error: errorMessage,
      };
    }
  },
};

// Advanced tools for enhanced functionality
export const watchDirectoryTool: Tool = {
  name: 'watch_directory',
  description: 'Start watching a directory for file changes',
  parameters: z.object({
    directory: z.string().describe('Directory to watch'),
    patterns: z.array(z.string()).optional().describe('File patterns to watch'),
    ignorePatterns: z.array(z.string()).optional().describe('Patterns to ignore'),
  }),
  execute: async (params, _context) => {
    try {
      const typedParams = params as { directory: string; patterns?: string[]; ignorePatterns?: string[] };
      const { directory, patterns = ['**/*'], ignorePatterns = [] } = typedParams;
      const chokidar = await import('chokidar');

      // Start watching the directory
      const watcher = chokidar.watch(directory, {
        ignored: ignorePatterns,
        persistent: true,
        ignoreInitial: true,
      });

      const watcherId = `watcher_${Date.now()}`;

      watcher.on('all', (event, path) => {
        logger.debug(`File ${event}: ${path}`, undefined, 'FileWatcher');
      });

      return {
        success: true,
        message: `Started watching directory: ${directory}`,
        data: {
          directory,
          patterns,
          ignorePatterns,
          watcherId,
          status: 'active',
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        message: `Failed to watch directory: ${(params as { directory?: string }).directory ?? 'unknown'}`,
        error: errorMessage,
      };
    }
  },
};

export const analyzeCodeTool: Tool = {
  name: 'analyze_code',
  description: 'Analyze code files for patterns, complexity, and issues',
  parameters: z.object({
    filePath: z.string().describe('Path to the code file to analyze'),
    analysisType: z.enum(['complexity', 'patterns', 'dependencies', 'all']).optional().describe('Type of analysis to perform'),
  }),
  execute: async (params, context) => {
    try {
      const typedParams = params as { filePath?: string; analysisType?: 'complexity' | 'patterns' | 'dependencies' | 'all' };
      const { filePath, analysisType = 'all' } = typedParams;

      // Handle empty parameters for testing
      if (!filePath) {
        return {
          success: false,
          message: 'File path is required for code analysis',
          error: 'MISSING_FILE_PATH',
        };
      }

      const fileResult = await fileOperations.readFile(filePath, context);

      if (!fileResult.success) {
        return fileResult;
      }

      const fileData = fileResult.data as { content?: string } | undefined;
      const content = typeof fileData?.content === 'string' ? fileData.content : '';
      const analysis: {
        file: string;
        size: number;
        lines: number;
        timestamp: string;
        complexity?: {
          cyclomaticComplexity: number;
          nestingDepth: number;
          functionCount: number;
        };
        patterns?: {
          todoComments: number;
          fixmeComments: number;
          consoleStatements: number;
          debugStatements: number;
        };
        dependencies?: {
          imports: number;
          exports: number;
        };
      } = {
        file: filePath,
        size: content.length,
        lines: content.split('\n').length,
        timestamp: new Date().toISOString(),
      };

      if (analysisType === 'complexity' || analysisType === 'all') {
        analysis.complexity = {
          cyclomaticComplexity: calculateCyclomaticComplexity(content),
          nestingDepth: calculateNestingDepth(content),
          functionCount: (content.match(/function\s+\w+|=>\s*{|class\s+\w+/g) ?? []).length,
        };
      }

      if (analysisType === 'patterns' || analysisType === 'all') {
        analysis.patterns = {
          todoComments: (content.match(/\/\/\s*TODO|\/\*\s*TODO|#\s*TODO/gi) ?? []).length,
          fixmeComments: (content.match(/\/\/\s*FIXME|\/\*\s*FIXME|#\s*FIXME/gi) ?? []).length,
          consoleStatements: (content.match(/console\.(log|error|warn|info)/g) ?? []).length,
          debugStatements: (content.match(/debugger|console\.debug/g) ?? []).length,
        };
      }

      if (analysisType === 'dependencies' || analysisType === 'all') {
        analysis.dependencies = {
          imports: (content.match(/import\s+.*from|require\s*\(/g) ?? []).length,
          exports: (content.match(/export\s+|module\.exports/g) ?? []).length,
        };
      }

      return {
        success: true,
        message: `Code analysis completed for: ${filePath}`,
        data: analysis,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        message: `Failed to analyze code: ${(params as { filePath?: string }).filePath ?? 'unknown file'}`,
        error: errorMessage,
      };
    }
  },
};

// Helper functions for code analysis
function calculateCyclomaticComplexity(content: string): number {
  const complexityKeywords = [
    'if', 'else', 'while', 'for', 'switch', 'case', 'catch', 'try',
    'break', 'continue', 'return'
  ];

  // Special operators that need different handling
  const operatorPatterns = [
    /&&/g,  // logical AND
    /\|\|/g, // logical OR
    /\?/g,   // ternary operator
  ];

  let complexity = 1; // Base complexity

  // Count keyword-based complexity
  for (const keyword of complexityKeywords) {
    const matches = content.match(new RegExp(`\\b${keyword}\\b`, 'g'));
    if (matches) {
      complexity += matches.length;
    }
  }

  // Count operator-based complexity
  for (const pattern of operatorPatterns) {
    const matches = content.match(pattern);
    if (matches) {
      complexity += matches.length;
    }
  }

  return complexity;
}

function calculateNestingDepth(content: string): number {
  let maxDepth = 0;
  let currentDepth = 0;

  for (const char of content) {
    if (char === '{') {
      currentDepth++;
      maxDepth = Math.max(maxDepth, currentDepth);
    } else if (char === '}') {
      currentDepth--;
    }
  }

  return maxDepth;
}

export const executeParallelToolsTool: Tool = {
  name: 'execute_parallel_tools',
  description: 'Execute multiple tools in parallel for improved performance',
  parameters: z.object({
    toolCalls: z.array(z.object({
      toolName: z.string().describe('Name of the tool to execute'),
      parameters: z.record(z.any()).describe('Parameters for the tool'),
    })).describe('Array of tool calls to execute in parallel'),
    maxConcurrency: z.number().optional().describe('Maximum number of concurrent executions'),
  }),
  execute: async (params, context) => {
    try {
      const typedParams = params as {
        toolCalls: Array<{ toolName: string; parameters: Record<string, unknown> }>;
        maxConcurrency?: number;
      };
      const { toolCalls, maxConcurrency = 3 } = typedParams;
      const results: Array<{
        toolName: string;
        success: boolean;
        error?: string;
        data?: unknown;
        message?: string;
        metadata?: Record<string, unknown>;
      }> = [];

      // Execute tools in batches to respect concurrency limit
      for (let i = 0; i < toolCalls.length; i += maxConcurrency) {
        const batch = toolCalls.slice(i, i + maxConcurrency);
        const batchPromises = batch.map(async (toolCall: { toolName: string; parameters: Record<string, unknown> }) => {
          try {
            const tool = toolRegistry.getTool(toolCall.toolName);
            if (!tool) {
              return {
                toolName: toolCall.toolName,
                success: false,
                error: 'Tool not found',
              };
            }

            const result = await tool.execute(toolCall.parameters, context);
            return {
              toolName: toolCall.toolName,
              ...result,
            };
          } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
              toolName: toolCall.toolName,
              success: false,
              error: errorMessage,
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      return {
        success: true,
        message: `Executed ${toolCalls.length} tools in parallel. ${successCount} succeeded, ${failureCount} failed.`,
        data: {
          results,
          summary: {
            total: toolCalls.length,
            successful: successCount,
            failed: failureCount,
            executionTime: Date.now(),
          },
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        message: 'Failed to execute parallel tools',
        error: errorMessage,
      };
    }
  },
};

// Network and system tools
export const networkRequestTool: Tool = {
  name: 'network_request',
  description: 'Make HTTP requests to external APIs or services',
  parameters: z.object({
    url: z.string().describe('URL to make the request to'),
    method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).optional().describe('HTTP method'),
    headers: z.record(z.string()).optional().describe('Request headers'),
    body: z.string().optional().describe('Request body (for POST/PUT/PATCH)'),
    timeout: z.number().optional().describe('Request timeout in milliseconds'),
  }),
  execute: async (params, _context) => {
    try {
      const typedParams = params as {
        url: string;
        method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
        headers?: Record<string, string>;
        body?: string;
        timeout?: number;
      };
      const { url, method = 'GET', headers = {}, body, timeout = 10000 } = typedParams;

      // Check if network access is allowed
      const cliConfig = config.getConfig();
      if (!cliConfig.tools.allowNetworkAccess) {
        return {
          success: false,
          message: 'Network access is disabled in configuration',
          error: 'NETWORK_DISABLED',
        };
      }

      const axios = await import('axios');

      const axiosConfig: {
        url: string;
        method: string;
        headers: Record<string, string>;
        timeout: number;
        validateStatus: () => boolean;
        data?: string;
      } = {
        url,
        method,
        headers,
        timeout,
        validateStatus: () => true, // Don't throw on HTTP error status
      };

      if (body !== undefined) {
        axiosConfig.data = body;
      }

      const response = await axios.default(axiosConfig);

      return {
        success: true,
        message: `HTTP ${method} request to ${url} completed`,
        data: {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers as Record<string, unknown>,
          data: response.data as unknown,
          url: response.config.url,
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorCode = error instanceof Error && 'code' in error ? (error as { code?: string }).code : undefined;
      return {
        success: false,
        message: `Network request failed: ${errorMessage}`,
        error: errorCode ?? 'NETWORK_ERROR',
      };
    }
  },
};

export const systemInfoTool: Tool = {
  name: 'get_system_info',
  description: 'Get detailed system information and environment details',
  parameters: z.object({
    includeEnv: z.boolean().optional().describe('Whether to include environment variables'),
    includeProcesses: z.boolean().optional().describe('Whether to include running processes info'),
  }),
  execute: async (params, _context) => {
    try {
      const typedParams = params as { includeEnv?: boolean; includeProcesses?: boolean };
      const { includeEnv = false, includeProcesses = false } = typedParams;
      const os = await import('os');

      const systemInfo: {
        platform: string;
        arch: string;
        nodeVersion: string;
        uptime: number;
        memory: {
          total: number;
          free: number;
          used: number;
          processUsage: NodeJS.MemoryUsage;
        };
        cpu: {
          model: string;
          cores: number;
          loadAverage: number[];
        };
        network: NodeJS.Dict<import('os').NetworkInterfaceInfo[]>;
        hostname: string;
        userInfo: import('os').UserInfo<string>;
        tmpdir: string;
        homedir: string;
        environment?: NodeJS.ProcessEnv;
        process?: {
          pid: number;
          ppid?: number;
          title: string;
          argv: string[];
          execPath: string;
          cwd: string;
        };
      } = {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        uptime: process.uptime(),
        memory: {
          total: os.totalmem(),
          free: os.freemem(),
          used: os.totalmem() - os.freemem(),
          processUsage: process.memoryUsage(),
        },
        cpu: {
          model: os.cpus()[0]?.model ?? 'Unknown',
          cores: os.cpus().length,
          loadAverage: os.loadavg(),
        },
        network: os.networkInterfaces(),
        hostname: os.hostname(),
        userInfo: os.userInfo(),
        tmpdir: os.tmpdir(),
        homedir: os.homedir(),
      };

      if (includeEnv) {
        systemInfo.environment = process.env;
      }

      if (includeProcesses) {
        systemInfo.process = {
          pid: process.pid,
          ppid: process.ppid,
          title: process.title,
          argv: process.argv,
          execPath: process.execPath,
          cwd: process.cwd(),
        };
      }

      return {
        success: true,
        message: 'System information retrieved successfully',
        data: systemInfo,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        message: 'Failed to retrieve system information',
        error: errorMessage,
      };
    }
  },
};

export const validateJsonTool: Tool = {
  name: 'validate_json',
  description: 'Validate and format JSON content',
  parameters: z.object({
    jsonContent: z.string().describe('JSON content to validate'),
    format: z.boolean().optional().describe('Whether to format the JSON'),
  }),
  execute: async (params, _context) => {
    try {
      const typedParams = params as { jsonContent: string; format?: boolean };
      const { jsonContent, format = false } = typedParams;

      // Parse JSON to validate
      const parsed: unknown = JSON.parse(jsonContent);

      const result: {
        valid: boolean;
        parsed: unknown;
        formatted?: string;
      } = {
        valid: true,
        parsed,
      };

      if (format) {
        result.formatted = JSON.stringify(parsed, null, 2);
      }

      return Promise.resolve({
        success: true,
        message: 'JSON validation successful',
        data: result,
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const positionMatch = errorMessage.match(/position (\d+)/);
      return Promise.resolve({
        success: false,
        message: 'JSON validation failed',
        data: {
          valid: false,
          error: errorMessage,
          position: positionMatch?.[1],
        },
      });
    }
  },
};

export const manageEnvironmentTool: Tool = {
  name: 'manage_environment',
  description: 'Get, set, or unset environment variables',
  parameters: z.object({
    action: z.enum(['get', 'set', 'unset', 'list']).describe('Action to perform'),
    variable: z.string().optional().describe('Environment variable name'),
    value: z.string().optional().describe('Value to set (for set action)'),
    pattern: z.string().optional().describe('Pattern to filter variables (for list action)'),
  }),
  execute: async (params, context) => {
    try {
      const typedParams = params as {
        action: 'get' | 'set' | 'unset' | 'list';
        variable?: string;
        value?: string;
        pattern?: string;
      };
      const { action, variable, value, pattern } = typedParams;

      switch (action) {
        case 'get': {
          if (!variable) {
            return Promise.resolve({
              success: false,
              message: 'Variable name is required for get action',
              error: 'MISSING_VARIABLE',
            });
          }

          const envValue = context.environment[variable];
          return Promise.resolve({
            success: true,
            message: `Environment variable retrieved: ${variable}`,
            data: {
              variable,
              value: envValue ?? null,
              exists: envValue !== undefined,
            },
          });
        }

        case 'set': {
          if (!variable || value === undefined) {
            return Promise.resolve({
              success: false,
              message: 'Variable name and value are required for set action',
              error: 'MISSING_PARAMETERS',
            });
          }

          // Update context environment
          context.environment[variable] = value;
          // Also set in process environment for current session
          process.env[variable] = value;

          return Promise.resolve({
            success: true,
            message: `Environment variable set: ${variable}`,
            data: {
              variable,
              value,
              action: 'set',
            },
          });
        }

        case 'unset': {
          if (!variable) {
            return Promise.resolve({
              success: false,
              message: 'Variable name is required for unset action',
              error: 'MISSING_VARIABLE',
            });
          }

          const existed = variable in context.environment;
          delete context.environment[variable];
          delete process.env[variable];

          return Promise.resolve({
            success: true,
            message: `Environment variable unset: ${variable}`,
            data: {
              variable,
              existed,
              action: 'unset',
            },
          });
        }

        case 'list': {
          let variables = Object.entries(context.environment);

          if (pattern) {
            const regex = new RegExp(pattern, 'i');
            variables = variables.filter(([key]) => regex.test(key));
          }

          return Promise.resolve({
            success: true,
            message: `Listed ${variables.length} environment variables`,
            data: {
              variables: variables.reduce((acc, [key, val]) => {
                acc[key] = val;
                return acc;
              }, {} as Record<string, string>),
              count: variables.length,
              pattern: pattern ?? null,
            },
          });
        }

        default: {
          const neverAction: never = action;
          return Promise.resolve({
            success: false,
            message: `Unknown action: ${String(neverAction)}`,
            error: 'INVALID_ACTION',
          });
        }
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return Promise.resolve({
        success: false,
        message: 'Failed to manage environment variable',
        error: errorMessage,
      });
    }
  },
};

export const monitorSystemTool: Tool = {
  name: 'monitor_system',
  description: 'Monitor system resources and performance metrics',
  parameters: z.object({
    duration: z.number().optional().describe('Monitoring duration in seconds (default: 5)'),
    interval: z.number().optional().describe('Sampling interval in seconds (default: 1)'),
    metrics: z.array(z.enum(['cpu', 'memory', 'disk', 'network'])).optional().describe('Metrics to monitor'),
  }),
  execute: async (params, _context) => {
    try {
      const typedParams = params as {
        duration?: number;
        interval?: number;
        metrics?: string[];
      };
      const { duration = 5, interval = 1, metrics = ['cpu', 'memory'] } = typedParams;
      const os = await import('os');

      const samples: Array<{
        timestamp: string;
        uptime: number;
        cpu?: {
          cores: number;
          model: string;
          loadAverage: number[];
        };
        memory?: {
          total: number;
          free: number;
          used: number;
          usagePercent: number;
          process: NodeJS.MemoryUsage;
        };
        disk?: {
          cwd: string;
        };
        network?: {
          interfaces: string[];
        };
      }> = [];
      const startTime = Date.now();

      for (let i = 0; i < duration; i++) {
        const sample: {
          timestamp: string;
          uptime: number;
          cpu?: {
            cores: number;
            model: string;
            loadAverage: number[];
          };
          memory?: {
            total: number;
            free: number;
            used: number;
            usagePercent: number;
            process: NodeJS.MemoryUsage;
          };
          disk?: {
            cwd: string;
          };
          network?: {
            interfaces: string[];
          };
        } = {
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
        };

        if (metrics.includes('cpu')) {
          const cpus = os.cpus();
          sample.cpu = {
            cores: cpus.length,
            model: cpus[0]?.model ?? 'Unknown',
            loadAverage: os.loadavg(),
          };
        }

        if (metrics.includes('memory')) {
          const totalMem = os.totalmem();
          const freeMem = os.freemem();
          const processMemory = process.memoryUsage();

          sample.memory = {
            total: totalMem,
            free: freeMem,
            used: totalMem - freeMem,
            usagePercent: ((totalMem - freeMem) / totalMem) * 100,
            process: processMemory,
          };
        }

        if (metrics.includes('disk')) {
          // Basic disk info - would need additional libraries for detailed disk monitoring
          sample.disk = {
            cwd: process.cwd(),
            // Note: Real disk usage would require platform-specific commands
          };
        }

        if (metrics.includes('network')) {
          sample.network = {
            interfaces: Object.keys(os.networkInterfaces()),
            // Note: Real network monitoring would require additional implementation
          };
        }

        samples.push(sample);

        if (i < duration - 1) {
          await new Promise(resolve => setTimeout(resolve, interval * 1000));
        }
      }

      const endTime = Date.now();

      return {
        success: true,
        message: `System monitoring completed for ${duration} seconds`,
        data: {
          duration: (endTime - startTime) / 1000,
          samples,
          summary: {
            sampleCount: samples.length,
            metrics,
            interval,
          },
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        message: 'Failed to monitor system',
        error: errorMessage,
      };
    }
  },
};

export const manageDependenciesTool: Tool = {
  name: 'manage_dependencies',
  description: 'Install, update, or remove project dependencies',
  parameters: z.object({
    action: z.enum(['install', 'update', 'remove', 'list', 'audit']).describe('Action to perform'),
    packages: z.array(z.string()).optional().describe('Package names for install/update/remove'),
    packageManager: z.enum(['npm', 'yarn', 'pnpm', 'pip', 'cargo', 'go']).optional().describe('Package manager to use'),
    dev: z.boolean().optional().describe('Install as dev dependency'),
    global: z.boolean().optional().describe('Install globally'),
  }),
  execute: async (params, context) => {
    try {
      const typedParams = params as {
        action: 'install' | 'update' | 'remove' | 'list' | 'audit';
        packages?: string[];
        packageManager?: 'npm' | 'yarn' | 'pnpm' | 'pip' | 'cargo' | 'go';
        dev?: boolean;
        global?: boolean;
      };
      const { action, packages = [], packageManager, dev = false, global = false } = typedParams;

      // Detect package manager if not specified
      let pm = packageManager;
      if (!pm) {
        const projectType = context.projectContext.type;
        const config = context.projectContext.configuration;

        if (projectType === 'nodejs' || projectType === 'web') {
          pm = (config.packageManager as 'npm' | 'yarn' | 'pnpm') ?? 'npm';
        } else if (projectType === 'python') {
          pm = 'pip';
        } else if (projectType === 'rust') {
          pm = 'cargo';
        } else if (projectType === 'go') {
          pm = 'go';
        } else {
          return {
            success: false,
            message: 'Cannot determine package manager for this project type',
            error: 'UNKNOWN_PACKAGE_MANAGER',
          };
        }
      }

      // Build command based on action and package manager
      let command = '';

      switch (pm) {
        case 'npm':
          switch (action) {
            case 'install':
              command = `npm install${global ? ' -g' : ''}${dev ? ' --save-dev' : ''} ${packages.join(' ')}`;
              break;
            case 'update':
              command = packages.length > 0 ? `npm update ${packages.join(' ')}` : 'npm update';
              break;
            case 'remove':
              command = `npm uninstall${global ? ' -g' : ''} ${packages.join(' ')}`;
              break;
            case 'list':
              command = `npm list${global ? ' -g' : ''} --depth=0`;
              break;
            case 'audit':
              command = 'npm audit';
              break;
          }
          break;

        case 'yarn':
          switch (action) {
            case 'install':
              command = packages.length > 0
                ? `yarn add${dev ? ' --dev' : ''} ${packages.join(' ')}`
                : 'yarn install';
              break;
            case 'update':
              command = packages.length > 0 ? `yarn upgrade ${packages.join(' ')}` : 'yarn upgrade';
              break;
            case 'remove':
              command = `yarn remove ${packages.join(' ')}`;
              break;
            case 'list':
              command = 'yarn list --depth=0';
              break;
            case 'audit':
              command = 'yarn audit';
              break;
          }
          break;

        case 'pip':
          switch (action) {
            case 'install':
              command = `pip install ${packages.join(' ')}`;
              break;
            case 'update':
              command = packages.length > 0
                ? `pip install --upgrade ${packages.join(' ')}`
                : 'pip list --outdated';
              break;
            case 'remove':
              command = `pip uninstall -y ${packages.join(' ')}`;
              break;
            case 'list':
              command = 'pip list';
              break;
            case 'audit':
              command = 'pip check';
              break;
          }
          break;

        default:
          return {
            success: false,
            message: `Package manager ${pm} not fully supported yet`,
            error: 'UNSUPPORTED_PACKAGE_MANAGER',
          };
      }

      if (!command) {
        return {
          success: false,
          message: `Invalid action ${action} for package manager ${pm}`,
          error: 'INVALID_ACTION',
        };
      }

      // Execute the command using shell operations
      const { shellOperations } = await import('@/operations/shell-ops');
      const result = await shellOperations.executeCommand(command, context, {
        timeout: 120000, // 2 minutes timeout for package operations
      });

      const response: {
        success: boolean;
        message: string;
        data: {
          action: string;
          packages: string[];
          packageManager: string;
          command: string;
          output: unknown;
        };
        error?: string;
      } = {
        success: result.success,
        message: result.success
          ? `Package ${action} completed successfully`
          : `Package ${action} failed`,
        data: {
          action,
          packages,
          packageManager: pm,
          command,
          output: result.data,
        },
      };

      if (!result.success && result.error) {
        response.error = result.error;
      }

      return response;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        message: 'Failed to manage dependencies',
        error: errorMessage,
      };
    }
  },
};

// Tool registry
export const allTools: Tool[] = [
  // File operations
  readFileTool,
  writeFileTool,
  deleteFileTool,
  copyFileTool,
  moveFileTool,
  createDirectoryTool,
  listDirectoryTool,
  searchFilesTool,
  grepFilesTool,
  getFileInfoTool,
  setPermissionsTool,

  // Shell operations
  executeCommandTool,
  executeScriptTool,
  killProcessTool,
  listProcessesTool,

  // Context operations
  getProjectContextTool,
  getSessionInfoTool,

  // Advanced operations
  watchDirectoryTool,
  analyzeCodeTool,
  executeParallelToolsTool,

  // Network and system operations
  networkRequestTool,
  systemInfoTool,
  validateJsonTool,
  manageEnvironmentTool,
  monitorSystemTool,
  manageDependenciesTool,
];

export class ToolRegistry {
  private tools: Map<string, Tool> = new Map();

  constructor() {
    this.registerTools(allTools);
  }

  public registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
    logger.debug(`Tool registered: ${tool.name}`, undefined, 'ToolRegistry');
  }

  public registerTools(tools: Tool[]): void {
    tools.forEach(tool => this.registerTool(tool));
  }

  public getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  public getAllTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  public getToolNames(): string[] {
    return Array.from(this.tools.keys());
  }

  public hasTools(name: string): boolean {
    return this.tools.has(name);
  }

  public getToolsByCategory(category: string): Tool[] {
    return this.getAllTools().filter(tool => 
      tool.name.startsWith(category) || tool.description.toLowerCase().includes(category)
    );
  }
}

export const toolRegistry = new ToolRegistry();
