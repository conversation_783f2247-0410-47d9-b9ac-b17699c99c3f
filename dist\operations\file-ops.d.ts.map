{"version": 3, "file": "file-ops.d.ts", "sourceRoot": "", "sources": ["../../src/operations/file-ops.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,gBAAgB,EAAE,MAAM,SAAS,CAAC;AAI3C,MAAM,WAAW,mBAAmB;IAClC,OAAO,EAAE,OAAO,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,qBAAa,cAAc;IACzB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAiB;WAE1B,WAAW,IAAI,cAAc;IAO9B,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAgDnF,SAAS,CACpB,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,gBAAgB,EACzB,OAAO,GAAE;QAAE,SAAS,CAAC,EAAE,OAAO,CAAC;QAAC,UAAU,CAAC,EAAE,OAAO,CAAA;KAAO,GAC1D,OAAO,CAAC,mBAAmB,CAAC;IAiDlB,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAkCrF,QAAQ,CACnB,UAAU,EAAE,MAAM,EAClB,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,gBAAgB,EACzB,OAAO,GAAE;QAAE,SAAS,CAAC,EAAE,OAAO,CAAA;KAAO,GACpC,OAAO,CAAC,mBAAmB,CAAC;IAqDlB,QAAQ,CACnB,UAAU,EAAE,MAAM,EAClB,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,gBAAgB,GACxB,OAAO,CAAC,mBAAmB,CAAC;IAkDlB,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAkCzF,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAoEvF,WAAW,CACtB,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,gBAAgB,EACzB,OAAO,GAAE;QACP,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,cAAc,CAAC,EAAE,OAAO,CAAC;QACzB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;KACjB,GACL,OAAO,CAAC,mBAAmB,CAAC;IA+FlB,SAAS,CACpB,UAAU,EAAE,MAAM,EAClB,OAAO,EAAE,gBAAgB,EACzB,OAAO,GAAE;QACP,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,aAAa,CAAC,EAAE,OAAO,CAAC;QACxB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,YAAY,CAAC,EAAE,MAAM,CAAC;KAClB,GACL,OAAO,CAAC,mBAAmB,CAAC;IAyGlB,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,mBAAmB,CAAC;IAkDtF,cAAc,CACzB,QAAQ,EAAE,MAAM,EAChB,WAAW,EAAE,MAAM,GAAG,MAAM,EAC5B,OAAO,EAAE,gBAAgB,GACxB,OAAO,CAAC,mBAAmB,CAAC;IAwC/B,OAAO,CAAC,YAAY;IAuBpB,OAAO,CAAC,WAAW;CAMpB;AAED,eAAO,MAAM,cAAc,gBAA+B,CAAC"}