{"version": 3, "file": "deepseek.js", "sourceRoot": "", "sources": ["../../src/providers/deepseek.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAE7C,2CAAwC;AAExC,MAAa,gBAAgB;IACX,IAAI,GAAG,UAAU,CAAC;IAC1B,MAAM,CAAgB;IAE9B;QACE,IAAI,CAAC,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;SACF,CAAC,CAAC;IACL,CAAC;IAEM,cAAc,CAAC,MAAmB;QACvC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,QAAwB,EAAE,MAAmB;QACpE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACrC,GAAG,MAAM,CAAC,OAAO,mBAAmB,EACpC;gBACE,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACvC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;gBACpC,MAAM,EAAE,KAAK;aACd,EACD;gBACE,OAAO,EAAE;oBACP,aAAa,EAAE,UAAU,MAAM,CAAC,MAAM,EAAE;iBACzC;aACF,CACF,CAAC;YAEF,MAAM,OAAO,GAAI,QAAQ,CAAC,IAA+D,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;YACvH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBACzC,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,MAAM,EAAG,QAAQ,CAAC,IAA4B,CAAC,KAAK;aACrD,EAAE,kBAAkB,CAAC,CAAC;YAEvB,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;YAE9D,MAAM,UAAU,GAAG,KAA6D,CAAC;YACjF,IAAI,UAAU,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACxC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,UAAU,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACxC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAC1D,CAAC;YAED,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,IAAI,eAAe,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,uBAAuB,YAAY,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,eAAe,CAC1B,QAAwB,EACxB,KAAa,EACb,MAAmB;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACrC,GAAG,MAAM,CAAC,OAAO,mBAAmB,EACpC;gBACE,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACvC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;gBAC9B,WAAW,EAAE,MAAM;gBACnB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;gBACtC,UAAU,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;gBACpC,MAAM,EAAE,KAAK;aACd,EACD;gBACE,OAAO,EAAE;oBACP,aAAa,EAAE,UAAU,MAAM,CAAC,MAAM,EAAE;iBACzC;aACF,CACF,CAAC;YAEF,MAAM,MAAM,GAAI,QAAQ,CAAC,IAAkD,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACvF,MAAM,OAAO,GAAG,MAAM,EAAE,OAAmE,CAAC;YAE5F,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBAC9C,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU;gBAClC,cAAc,EAAE,OAAO,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC;aAChD,EAAE,kBAAkB,CAAC,CAAC;YAEvB,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,IAAa,EAAE,EAAE;gBAC1D,MAAM,QAAQ,GAAG,IAAqE,CAAC;gBACvF,OAAO;oBACL,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,IAAI,EAAE,UAAmB;oBACzB,QAAQ,EAAE;wBACR,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;wBAC5B,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,SAAS;qBACvC;iBACF,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;gBAC9B,GAAG,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,CAAC;aAChC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;YAEnE,MAAM,UAAU,GAAG,KAA6D,CAAC;YACjF,IAAI,UAAU,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACxC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,UAAU,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACxC,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAC1D,CAAC;YAED,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,IAAI,eAAe,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,uBAAuB,YAAY,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,QAAwB;QAC7C,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACxB,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACxB,OAAO;oBACL,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,YAAY,EAAE,GAAG,CAAC,UAAU;iBAC7B,CAAC;YACJ,CAAC;YAED,MAAM,SAAS,GAA4B;gBACzC,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC;YAEF,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;gBAClB,SAAS,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACnD,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE;wBACR,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;wBACxB,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS;qBACnC;iBACF,CAAC,CAAC,CAAC;YACN,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,KAAa;QAC/B,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;aAClD;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,eAAe,CAAC,MAAe;QACrC,sCAAsC;QACtC,wEAAwE;QACxE,MAAM,SAAS,GAAG,MAA0I,CAAC;QAE7J,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,KAAK,WAAW,EAAE,CAAC;YAC7C,MAAM,UAAU,GAA4B,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;YACvC,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBACjD,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAC9C,MAAM,WAAW,GAAG,KAA0C,CAAC;oBAC/D,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC;wBAChC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACrB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,UAAU;gBACV,QAAQ,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;aACrD,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,KAAK,WAAW,EAAE,CAAC;YAC7C,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QAC5B,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,KAAK,WAAW,EAAE,CAAC;YAC7C,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QAC5B,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC9C,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,KAAK,UAAU,EAAE,CAAC;YAC5C,OAAO;gBACL,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;aACjD,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,WAAW;IACxC,CAAC;CACF;AAlOD,4CAkOC"}