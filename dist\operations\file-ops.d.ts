import { ExecutionContext } from '../types';
export interface FileOperationResult {
    success: boolean;
    message: string;
    data?: unknown;
    error?: string;
}
export declare class FileOperations {
    private static instance;
    static getInstance(): FileOperations;
    readFile(filePath: string, context: ExecutionContext): Promise<FileOperationResult>;
    writeFile(filePath: string, content: string, context: ExecutionContext, options?: {
        overwrite?: boolean;
        createDirs?: boolean;
    }): Promise<FileOperationResult>;
    deleteFile(filePath: string, context: ExecutionContext): Promise<FileOperationResult>;
    copyFile(sourcePath: string, destPath: string, context: ExecutionContext, options?: {
        overwrite?: boolean;
    }): Promise<FileOperationResult>;
    moveFile(sourcePath: string, destPath: string, context: ExecutionContext): Promise<FileOperationResult>;
    createDirectory(dirPath: string, context: ExecutionContext): Promise<FileOperationResult>;
    listDirectory(dirPath: string, context: ExecutionContext): Promise<FileOperationResult>;
    searchFiles(pattern: string, context: ExecutionContext, options?: {
        directory?: string;
        includeContent?: boolean;
        maxResults?: number;
        fileTypes?: string[];
    }): Promise<FileOperationResult>;
    grepFiles(searchText: string, context: ExecutionContext, options?: {
        directory?: string;
        filePattern?: string;
        caseSensitive?: boolean;
        maxResults?: number;
        contextLines?: number;
    }): Promise<FileOperationResult>;
    getFileInfo(filePath: string, context: ExecutionContext): Promise<FileOperationResult>;
    setPermissions(filePath: string, permissions: string | number, context: ExecutionContext): Promise<FileOperationResult>;
    private validatePath;
    private resolvePath;
}
export declare const fileOperations: FileOperations;
//# sourceMappingURL=file-ops.d.ts.map