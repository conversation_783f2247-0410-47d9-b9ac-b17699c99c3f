import axios, { AxiosInstance } from 'axios';
import { <PERSON><PERSON>rovider, AgentMessage, AgentConfig, Tool, ToolCall } from '@/types';
import { logger } from '@/utils/logger';

export class DeepseekProvider implements LLMProvider {
  public readonly name = 'deepseek';
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      timeout: 60000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  public validateConfig(config: AgentConfig): boolean {
    return !!(config.apiKey && config.baseUrl);
  }

  public async sendMessage(messages: AgentMessage[], config: AgentConfig): Promise<string> {
    try {
      const response = await this.client.post(
        `${config.baseUrl}/chat/completions`,
        {
          model: config.model,
          messages: this.formatMessages(messages),
          temperature: config.temperature ?? 0.7,
          max_tokens: config.maxTokens ?? 4000,
          stream: false,
        },
        {
          headers: {
            Authorization: `Bearer ${config.apiKey}`,
          },
        }
      );

      const content = (response.data as { choices: Array<{ message?: { content?: string } }> }).choices[0]?.message?.content;
      if (!content) {
        throw new Error('No content in response');
      }

      logger.debug('Deepseek response received', {
        model: config.model,
        tokens: (response.data as { usage?: unknown }).usage
      }, 'DeepseekProvider');

      return content;
    } catch (error: unknown) {
      logger.error('Deepseek API error', error, 'DeepseekProvider');

      const axiosError = error as { response?: { status?: number }; message?: string };
      if (axiosError.response?.status === 401) {
        throw new Error('Invalid API key for Deepseek');
      }

      if (axiosError.response?.status === 429) {
        throw new Error('Rate limit exceeded for Deepseek API');
      }

      const errorMessage = axiosError.message ?? 'Unknown error';
      throw new Error(`Deepseek API error: ${errorMessage}`);
    }
  }

  public async sendToolMessage(
    messages: AgentMessage[], 
    tools: Tool[], 
    config: AgentConfig
  ): Promise<{ message: string; toolCalls?: ToolCall[] }> {
    try {
      const response = await this.client.post(
        `${config.baseUrl}/chat/completions`,
        {
          model: config.model,
          messages: this.formatMessages(messages),
          tools: this.formatTools(tools),
          tool_choice: 'auto',
          temperature: config.temperature ?? 0.7,
          max_tokens: config.maxTokens ?? 4000,
          stream: false,
        },
        {
          headers: {
            Authorization: `Bearer ${config.apiKey}`,
          },
        }
      );

      const choice = (response.data as { choices: Array<{ message?: unknown }> }).choices[0];
      const message = choice?.message as { content?: string; tool_calls?: unknown[] } | undefined;

      if (!message) {
        throw new Error('No message in response');
      }

      logger.debug('Deepseek tool response received', {
        model: config.model,
        hasToolCalls: !!message.tool_calls,
        toolCallsCount: message.tool_calls?.length ?? 0
      }, 'DeepseekProvider');

      const toolCalls = message.tool_calls?.map((call: unknown) => {
        const toolCall = call as { id: string; function: { name: string; arguments: string } };
        return {
          id: toolCall.id,
          type: 'function' as const,
          function: {
            name: toolCall.function.name,
            arguments: toolCall.function.arguments,
          },
        };
      });

      return {
        message: message.content ?? '',
        ...(toolCalls && { toolCalls }),
      };
    } catch (error: unknown) {
      logger.error('Deepseek tool API error', error, 'DeepseekProvider');

      const axiosError = error as { response?: { status?: number }; message?: string };
      if (axiosError.response?.status === 401) {
        throw new Error('Invalid API key for Deepseek');
      }

      if (axiosError.response?.status === 429) {
        throw new Error('Rate limit exceeded for Deepseek API');
      }

      const errorMessage = axiosError.message ?? 'Unknown error';
      throw new Error(`Deepseek API error: ${errorMessage}`);
    }
  }

  private formatMessages(messages: AgentMessage[]): unknown[] {
    return messages.map(msg => {
      if (msg.role === 'tool') {
        return {
          role: 'tool',
          content: msg.content,
          tool_call_id: msg.toolCallId,
        };
      }

      const formatted: Record<string, unknown> = {
        role: msg.role,
        content: msg.content,
      };

      if (msg.toolCalls) {
        formatted['tool_calls'] = msg.toolCalls.map(call => ({
          id: call.id,
          type: call.type,
          function: {
            name: call.function.name,
            arguments: call.function.arguments,
          },
        }));
      }

      return formatted;
    });
  }

  private formatTools(tools: Tool[]): unknown[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: this.zodToJsonSchema(tool.parameters),
      },
    }));
  }

  private zodToJsonSchema(schema: unknown): unknown {
    // Basic Zod to JSON Schema conversion
    // This is a simplified version - you might want to use a proper library
    const zodSchema = schema as { _def?: { typeName?: string; shape?: () => Record<string, unknown>; type?: unknown; innerType?: unknown; optional?: boolean } };

    if (zodSchema._def?.typeName === 'ZodObject') {
      const properties: Record<string, unknown> = {};
      const required: string[] = [];

      const shape = zodSchema._def.shape?.();
      if (shape) {
        for (const [key, value] of Object.entries(shape)) {
          properties[key] = this.zodToJsonSchema(value);
          const valueSchema = value as { _def?: { optional?: boolean } };
          if (!valueSchema._def?.optional) {
            required.push(key);
          }
        }
      }

      return {
        type: 'object',
        properties,
        required: required.length > 0 ? required : undefined,
      };
    }

    if (zodSchema._def?.typeName === 'ZodString') {
      return { type: 'string' };
    }

    if (zodSchema._def?.typeName === 'ZodNumber') {
      return { type: 'number' };
    }

    if (zodSchema._def?.typeName === 'ZodBoolean') {
      return { type: 'boolean' };
    }

    if (zodSchema._def?.typeName === 'ZodArray') {
      return {
        type: 'array',
        items: this.zodToJsonSchema(zodSchema._def.type),
      };
    }

    if (zodSchema._def?.typeName === 'ZodOptional') {
      return this.zodToJsonSchema(zodSchema._def.innerType);
    }

    return { type: 'string' }; // fallback
  }
}
