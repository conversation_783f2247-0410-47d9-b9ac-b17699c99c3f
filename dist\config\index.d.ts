import { CLIConfig } from '../types';
export declare class ConfigManager {
    private static instance;
    private config;
    private configPath;
    private constructor();
    static getInstance(): ConfigManager;
    private loadConfig;
    private getDefaultConfig;
    getConfig(): CLIConfig;
    updateConfig(updates: Partial<CLIConfig>): void;
    saveConfig(): void;
    resetConfig(): void;
    getConfigPath(): string;
    getDataDirectory(): string;
    getSessionsDirectory(): string;
    getCacheDirectory(): string;
    getLogsDirectory(): string;
    ensureDirectories(): void;
    validateProviderConfig(provider: 'deepseek' | 'ollama'): boolean;
    getProviderConfig(provider: 'deepseek' | 'ollama'): {
        baseUrl: string;
        defaultModel: string;
    };
    setProviderApiKey(provider: 'deepseek' | 'ollama', apiKey: string): void;
    getEnvironmentVariables(): Record<string, string>;
}
export declare const config: ConfigManager;
//# sourceMappingURL=index.d.ts.map