export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}
export interface LogEntry {
    timestamp: Date;
    level: LogLevel;
    message: string;
    data?: unknown;
    sessionId?: string;
    component?: string;
}
export declare class Logger {
    private static instance;
    private logLevel;
    private logFile;
    private constructor();
    static getInstance(): Logger;
    setLogLevel(level: LogLevel): void;
    debug(message: string, data?: unknown, component?: string, sessionId?: string): void;
    info(message: string, data?: unknown, component?: string, sessionId?: string): void;
    warn(message: string, data?: unknown, component?: string, sessionId?: string): void;
    error(message: string, data?: unknown, component?: string, sessionId?: string): void;
    private log;
    private logToConsole;
    private logToFile;
    getLogs(sessionId?: string, component?: string, level?: LogLevel): LogEntry[];
    clearLogs(): void;
    getLogFile(): string;
}
export declare const logger: Logger;
//# sourceMappingURL=logger.d.ts.map