{"version": 3, "file": "ollama.js", "sourceRoot": "", "sources": ["../../src/providers/ollama.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAE7C,2CAAwC;AAExC,MAAa,cAAc;IACT,IAAI,GAAG,QAAQ,CAAC;IACxB,MAAM,CAAgB;IAE9B;QACE,IAAI,CAAC,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,MAAM,EAAE,uBAAuB;YACxC,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;SACF,CAAC,CAAC;IACL,CAAC;IAEM,cAAc,CAAC,MAAmB;QACvC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,QAAwB,EAAE,MAAmB;QACpE,IAAI,CAAC;YACH,oCAAoC;YACpC,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAExC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACrC,GAAG,MAAM,CAAC,OAAO,WAAW,EAC5B;gBACE,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;gBACvC,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,WAAW,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;iBACtC;aACF,CACF,CAAC;YAEF,MAAM,OAAO,GAAI,QAAQ,CAAC,IAA2D,CAAC,OAAO,EAAE,OAAO,CAAC;YACvG,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACvC,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,IAAI,EAAG,QAAQ,CAAC,IAA2B,CAAC,IAAI;aACjD,EAAE,gBAAgB,CAAC,CAAC;YAErB,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAE1D,MAAM,SAAS,GAAG,KAA4E,CAAC;YAC/F,IAAI,SAAS,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAC5E,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,CAAC,KAAK,sBAAsB,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,IAAI,eAAe,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,eAAe,CAC1B,QAAwB,EACxB,KAAa,EACb,MAAmB;QAEnB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAExC,gFAAgF;YAChF,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAClD,MAAM,gBAAgB,GAAG;gBACvB;oBACE,IAAI,EAAE,QAAiB;oBACvB,OAAO,EAAE,WAAW;iBACrB;gBACD,GAAG,QAAQ;aACZ,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACrC,GAAG,MAAM,CAAC,OAAO,WAAW,EAC5B;gBACE,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC;gBAC/C,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE;oBACP,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;oBACtC,WAAW,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;iBACtC;aACF,CACF,CAAC;YAEF,MAAM,YAAY,GAAG,QAAQ,CAAC,IAA0C,CAAC;YACzE,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YAED,qCAAqC;YACrC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAE5D,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,YAAY,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC;gBAClC,cAAc,EAAE,SAAS,CAAC,MAAM;aACjC,EAAE,gBAAgB,CAAC,CAAC;YAErB,MAAM,MAAM,GAAgD;gBAC1D,OAAO;aACR,CAAC;YAEF,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;YAC/B,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACxB,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAE/D,MAAM,SAAS,GAAG,KAA4E,CAAC;YAC/F,IAAI,SAAS,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAC5E,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,SAAS,MAAM,CAAC,KAAK,sBAAsB,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,IAAI,eAAe,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,MAAmB;QACpD,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,OAAO,WAAW,CAAC,CAAC;YACrE,MAAM,YAAY,GAAG,QAAQ,CAAC,IAA4C,CAAC;YAC3E,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC;YAE3E,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,eAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,mCAAmC,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;gBACnG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,MAAmB;QACzC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,WAAW,EAAE;gBACnD,IAAI,EAAE,MAAM,CAAC,KAAK;aACnB,CAAC,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,gBAAgB,CAAC,CAAC;QACxF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,CAAC,KAAK,KAAK,YAAY,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,QAAwB;QAC7C,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1B,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI;YAClD,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACrC,MAAM,gBAAgB,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACxC,OAAO,SAAS,IAAI,CAAC,IAAI;eAChB,IAAI,CAAC,WAAW;cACjB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;QAC3E,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhB,OAAO;;;;;;;;;;;;;;;;EAgBT,gBAAgB;;8EAE4D,CAAC;IAC7E,CAAC;IAEO,cAAc,CAAC,OAAe;QACpC,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YAClE,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAA+B,CAAC;gBACtE,IAAI,MAAM,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC1D,OAAO;wBACL,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE;wBACjD,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAa,EAAE,KAAa,EAAE,EAAE;4BAChE,MAAM,QAAQ,GAAG,IAAsE,CAAC;4BACxF,OAAO;gCACL,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,EAAE;gCACnD,IAAI,EAAE,UAAmB;gCACzB,QAAQ,EAAE;oCACR,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;oCAC/B,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;iCAC1C;6BACF,CAAC;wBACJ,CAAC,CAAC;qBACH,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;QACpF,CAAC;QAED,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,EAAE;SACd,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,MAAe;QACrC,sCAAsC;QACtC,MAAM,SAAS,GAAG,MAA0I,CAAC;QAE7J,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,WAAW,EAAE,CAAC;YACjD,MAAM,UAAU,GAA4B,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAa,EAAE,CAAC;YAE9B,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;YAC1C,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBACjD,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAC9C,MAAM,WAAW,GAAG,KAA0C,CAAC;oBAC/D,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC;wBACpC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACrB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,UAAU;gBACV,QAAQ,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;aACrD,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,WAAW,EAAE,CAAC;YACjD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QAC5B,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,WAAW,EAAE,CAAC;YACjD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;QAC5B,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,YAAY,EAAE,CAAC;YAClD,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,UAAU,EAAE,CAAC;YAChD,OAAO;gBACL,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACpD,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,aAAa,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,WAAW;IACxC,CAAC;CACF;AA1RD,wCA0RC"}